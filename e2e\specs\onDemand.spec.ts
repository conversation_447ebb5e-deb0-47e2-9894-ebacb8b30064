import { test, expect } from '../fixtures/auth';
import { onDemandPage } from '../pages/OnDemandPage';

test.describe('On-Demand Search page validations', () => {
  test('Check data in search grid', async ({ context }) => {
    const page = await context.newPage();
    const OnDemand = new onDemandPage(page);

    await page.goto('https://mdmxref.test.vizientinc.com/');

    // 🔹 Open the On-Demand Search page
    await expect(OnDemand.onDemandButton).toBeVisible({ timeout: 10000 });
    await expect(OnDemand.onDemandButton).toHaveText('On-Demand Search');
    await OnDemand.onDemandButton.click();

    // 🔹 Open Search Parameters panel (basic interaction check)
    await expect(OnDemand.searchParametersButton).toBeVisible();
    await OnDemand.searchParametersButton.click();

    // 🚧 TODO: Add validations for grid, filters, results, etc. when ready
  });
});