// e2e/config/database.ts
import { config } from 'mssql';

export interface DatabaseConfig {
  server: string;
  database: string;
  user?: string;
  password?: string;
  port?: number;
  authentication?: {
    type: 'default' | 'azure-active-directory-password' | 'azure-active-directory-access-token';
    options?: {
      userName?: string;
      password?: string;
      domain?: string;
      clientId?: string;
      clientSecret?: string;
      tenantId?: string;
    };
  };
  options?: {
    encrypt?: boolean;
    trustServerCertificate?: boolean;
    enableArithAbort?: boolean;
  };
}

// Database configurations for different environments
export const databaseConfigs: Record<string, DatabaseConfig> = {
  test: {
    server: process.env.DB_TEST_SERVER || 'vzn-eastus2-mdmxrefv3-test-sql-01.database.windows.net',
    database: process.env.DB_TEST_DATABASE || 'MDMXREFDB',
    user: process.env.DB_TEST_USER || '',
    password: process.env.DB_TEST_PASSWORD || '',
    port: parseInt(process.env.DB_TEST_PORT || '1433'),
    authentication: {
      type: (process.env.DB_TEST_USER && process.env.DB_TEST_PASSWORD)
        ? 'azure-active-directory-password'
        : 'default',
      options: {
        userName: process.env.DB_TEST_USER || '',
        password: process.env.DB_TEST_PASSWORD || '',
        domain: process.env.DB_TEST_DOMAIN || 'vizientinc.com',
      },
    },
    options: {
      encrypt: true,
      trustServerCertificate: false,
      enableArithAbort: true,
    },
  },
  staging: {
    server: process.env.DB_STAGING_SERVER || 'mdmxref-staging-db.vizientinc.com',
    database: process.env.DB_STAGING_DATABASE || 'MDMXref_Staging',
    user: process.env.DB_STAGING_USER || 'mdmxref_staging_user',
    password: process.env.DB_STAGING_PASSWORD || '',
    port: parseInt(process.env.DB_STAGING_PORT || '1433'),
    options: {
      encrypt: true,
      trustServerCertificate: true,
      enableArithAbort: true,
    },
  },
  production: {
    server: process.env.DB_PROD_SERVER || 'mdmxref-prod-db.vizientinc.com',
    database: process.env.DB_PROD_DATABASE || 'MDMXref_Production',
    user: process.env.DB_PROD_USER || 'mdmxref_prod_user',
    password: process.env.DB_PROD_PASSWORD || '',
    port: parseInt(process.env.DB_PROD_PORT || '1433'),
    options: {
      encrypt: true,
      trustServerCertificate: true,
      enableArithAbort: true,
    },
  },
};

// Get the current environment configuration
export function getDatabaseConfig(): DatabaseConfig {
  const environment = process.env.TEST_ENVIRONMENT || 'test';
  const config = databaseConfigs[environment];
  
  if (!config) {
    throw new Error(`Database configuration not found for environment: ${environment}`);
  }
  
  if (!config.password) {
    throw new Error(`Database password not configured for environment: ${environment}`);
  }
  
  return config;
}

// Convert to mssql config format
export function getMSSQLConfig(): config {
  const dbConfig = getDatabaseConfig();

  const baseConfig: config = {
    server: dbConfig.server,
    database: dbConfig.database,
    port: dbConfig.port,
    options: dbConfig.options,
    pool: {
      max: 10,
      min: 0,
      idleTimeoutMillis: 30000,
    },
    requestTimeout: 30000,
    connectionTimeout: 30000,
  };

  // Handle authentication based on type
  if (dbConfig.authentication?.type === 'azure-active-directory-password' &&
      dbConfig.authentication.options?.userName &&
      dbConfig.authentication.options?.password) {
    baseConfig.authentication = {
      type: 'azure-active-directory-password',
      options: {
        userName: dbConfig.authentication.options.userName,
        password: dbConfig.authentication.options.password,
      },
    };
  } else if (dbConfig.user && dbConfig.password) {
    // Use traditional SQL Server authentication
    baseConfig.user = dbConfig.user;
    baseConfig.password = dbConfig.password;
  } else {
    // Try Windows authentication or default
    baseConfig.authentication = {
      type: 'default',
    };
  }

  return baseConfig;
}
