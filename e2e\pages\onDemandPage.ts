// e2e/pages/onDemandPage.ts
import { Page, Locator } from '@playwright/test';

export class onDemandPage {
  readonly page: Page;

  // 🔹 Selectors for On-Demand Search page
  readonly onDemandButton: Locator;
  readonly searchParametersButton: Locator;

  constructor(page: Page) {
    this.page = page;

    // Using current selectors — swap for data-testlabel later
    this.onDemandButton = page.getByRole('button', { name: 'On-Demand Search' });
    this.searchParametersButton = page.getByRole('button', { name: 'Search Parameters' });
  }
}