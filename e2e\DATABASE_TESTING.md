# Database Testing Guide

This guide explains how to use real database data in your E2E tests for the MDMXref application.

## Overview

The database testing system allows you to:
- Connect to the MDMXref database during tests
- Retrieve real product, vendor, and category data
- Use actual data for more realistic test scenarios
- Cache data for better performance
- Validate test results against known database content

## Setup

### 1. Environment Configuration

Copy the example environment file and configure your database connection:

```bash
cp .env.example .env
```

Edit `.env` and fill in your database connection details:

```env
# Test Environment
TEST_ENVIRONMENT=test

# Test Database Configuration
DB_TEST_SERVER=your-test-db-server.com
DB_TEST_DATABASE=MDMXref_Test
DB_TEST_USER=your_test_user
DB_TEST_PASSWORD=your_test_password
DB_TEST_PORT=1433
```

### 2. Install Dependencies

The database testing requires additional dependencies:

```bash
npm install
```

This will install:
- `mssql` - SQL Server driver
- `dotenv` - Environment variable management
- `@types/mssql` - TypeScript definitions

### 3. Validate Database Connection

Test your database connection:

```bash
npm run db:validate
```

This will:
- Connect to the database
- Verify test data is available
- Report any connection issues

## Usage

### Running Database-Enabled Tests

```bash
# Run On-Demand tests with database data
npm run test:ondemand:db

# Run in debug mode (headed browser, slower execution)
npm run test:ondemand:db:debug

# Validate database connection and run tests
npm run test:with-db
```

### Test Data Service

The `TestDataService` provides access to real database data:

```typescript
import { testDataHelper } from '../services/testDataService';

// Get real product keys for testing
const productKeys = await testDataHelper.getProductKeyTestData();

// Get real vendor names
const vendorNames = await testDataHelper.getVendorNameTestData();

// Get a complete test data set (related data)
const completeData = await testDataHelper.getCompleteTestDataSet();
```

### Available Test Data Methods

#### Product Data
- `getProductKeyTestData()` - Returns array of real product keys
- `getVendorPartNumberTestData()` - Returns array of real vendor part numbers
- `getProductDescriptionTestData()` - Returns array of real product descriptions

#### Vendor Data
- `getVendorNameTestData()` - Returns array of real vendor names
- `getVendorSpecificTestData(vendorName)` - Returns products for specific vendor

#### Category Data
- `getContractCategoryTestData()` - Returns array of real contract categories
- `getCategorySpecificTestData(categoryName)` - Returns products for specific category

#### Complete Data Sets
- `getCompleteTestDataSet()` - Returns related data (product key, vendor, part number, etc.)
- `getValidSearchData()` - Returns comprehensive test data for all search fields

## Test Examples

### Basic Search Test with Real Data

```typescript
test('Search with real Product Key', async ({ context, testData }) => {
  const page = await context.newPage();
  const onDemand = new onDemandPage(page);

  // Get real product key from database
  const productKeys = await testDataHelper.getProductKeyTestData();
  const testProductKey = productKeys[0];

  // Use real data in test
  await onDemand.fillProductKey(testProductKey);
  await onDemand.executeSearch();
  
  // Validate results
  await onDemand.waitForResults();
  const hasResults = await onDemand.hasResults();
  expect(hasResults).toBe(true);
});
```

### Multi-Field Search with Related Data

```typescript
test('Multi-field search with related data', async ({ context, testData }) => {
  const page = await context.newPage();
  const onDemand = new onDemandPage(page);

  // Get complete related data set
  const completeData = await testDataHelper.getCompleteTestDataSet();
  
  // Fill multiple fields with related data
  await onDemand.fillProductKey(completeData.productKey);
  await onDemand.fillVendorPartNumber(completeData.vendorPartNumber);
  await onDemand.fillVendorProductDescription(completeData.productDescription);
  
  await onDemand.executeSearch();
  await onDemand.waitForResults();
});
```

## Database Schema Assumptions

The test data service assumes the following database schema:

### Products Table
- `ProductKey` - Unique product identifier
- `VendorPartNumber` - Vendor's part number
- `VendorProductDescription` - Product description
- `VendorId` - Foreign key to Vendors table
- `CategoryId` - Foreign key to ContractCategories table
- `IsActive` - Boolean flag for active products

### Vendors Table
- `VendorId` - Primary key
- `VendorName` - Vendor name
- `VendorCode` - Vendor code
- `IsActive` - Boolean flag for active vendors

### ContractCategories Table
- `CategoryId` - Primary key
- `CategoryName` - Category name
- `CategoryCode` - Category code
- `IsActive` - Boolean flag for active categories

### CrossReferences Table
- `CrossReferenceId` - Primary key
- `InputProductKey` - Input product key
- `OutputProductKey` - Output product key
- `ConfidenceLevel` - Confidence level of the cross-reference
- `IsValid` - Boolean flag for valid cross-references

## Performance Considerations

### Caching
- Test data is cached for 5 minutes by default
- Cache can be cleared with `testDataHelper.clearCache()`
- Cache statistics available with `testDataService.getCacheStats()`

### Connection Management
- Database connections are pooled and reused
- Global setup/teardown manages connection lifecycle
- Individual tests don't create new connections

### Query Optimization
- Queries use `TOP` clauses to limit result sets
- Only active records are retrieved
- Indexes should exist on commonly queried fields

## Troubleshooting

### Connection Issues
```bash
# Test database connection
npm run db:validate

# Check connection details in .env file
# Verify network access to database server
# Confirm credentials are correct
```

### No Test Data Available
```bash
# Verify database contains test data
# Check that IsActive flags are set correctly
# Ensure test user has read permissions
```

### Performance Issues
```bash
# Clear test data cache
# Check database server performance
# Review query execution plans
```

## Security Notes

- Use read-only database users for testing
- Never use production database for testing
- Store credentials in `.env` file (not committed to git)
- Use separate test databases when possible
- Limit database access to test environments only

## Environment-Specific Configuration

### Test Environment
- Use dedicated test database
- Can contain synthetic or anonymized data
- Safe for destructive operations

### Staging Environment
- Use staging database replica
- Contains production-like data
- Read-only access recommended

### Production Environment
- **NOT RECOMMENDED** for automated testing
- If absolutely necessary, use read-only access
- Implement strict access controls
