{"name": "MDMXref-e2e", "version": "1.0.0", "type": "module", "scripts": {"save-admin-auth": "tsx e2e/utils/save-auth-admin.ts", "test:all": "playwright test --project=testEnv", "report:pdf": "tsx e2e/utils/export-report-to-pdf.ts", "report:snapshot": "tsx e2e/utils/snapshot-report.ts", "test:all:pdf:snapshot": "npm run test:all && npm run report:pdf && npm run report:snapshot"}, "devDependencies": {"@playwright/test": "^1.54.1", "ts-node": "^10.9.2", "typescript": "^5.8.3", "tsx": "^4.7.0"}}