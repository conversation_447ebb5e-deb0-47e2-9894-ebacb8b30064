{"name": "MDMXref-e2e", "version": "1.0.0", "type": "module", "scripts": {"save-admin-auth": "tsx e2e/utils/save-auth-admin.ts", "db:setup": "tsx e2e/utils/setupDatabase.ts", "db:validate": "tsx -e \"import('./e2e/utils/setupDatabase.js').then(m => m.validateTestEnvironment().then(r => process.exit(r ? 0 : 1)))\"", "test:all": "playwright test --project=testEnv", "test:ondemand": "playwright test e2e/specs/onDemand.spec.ts --project=testEnv", "test:ondemand:debug": "playwright test e2e/specs/onDemand.spec.ts --project=debug", "test:ondemand:db": "playwright test e2e/specs/onDemandWithDatabase.spec.ts --project=testEnv", "test:ondemand:db:debug": "playwright test e2e/specs/onDemandWithDatabase.spec.ts --project=debug", "test:with-db": "npm run db:validate && npm run test:ondemand:db", "report:pdf": "tsx e2e/utils/export-report-to-pdf.ts", "report:snapshot": "tsx e2e/utils/snapshot-report.ts", "test:all:pdf:snapshot": "npm run test:all && npm run report:pdf && npm run report:snapshot"}, "devDependencies": {"@playwright/test": "^1.54.1", "ts-node": "^10.9.2", "typescript": "^5.8.3", "tsx": "^4.7.0", "mssql": "^11.0.1", "dotenv": "^16.4.5", "@types/mssql": "^9.1.5"}}